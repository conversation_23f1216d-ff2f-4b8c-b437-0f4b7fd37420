-- +goose Up

-- Add program_id to workout_sessions table if it doesn't exist
-- +goose StatementBegin
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workout_sessions' AND column_name = 'program_id'
    ) THEN
        ALTER TABLE workout_sessions
        ADD COLUMN program_id INTEGER REFERENCES workout_programs(id) ON DELETE RESTRICT;
    END IF;
END $$;
-- +goose StatementEnd

-- Create index for program_id if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_workout_sessions_program_id ON workout_sessions(program_id);

-- +goose Down
DROP INDEX IF EXISTS idx_workout_sessions_program_id;
ALTER TABLE workout_sessions DROP COLUMN IF EXISTS program_id;
