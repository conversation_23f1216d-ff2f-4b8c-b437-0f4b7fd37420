package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"exert-app-service/cmd/server/testutil"
	"exert-app-service/internal/session"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWorkoutSession(t *testing.T) {
	ts := testutil.SetupTest(t)
	defer ts.Pool.Close()

	var testSession1 session.WorkoutSessionDao
	var testSession2 session.WorkoutSessionDao

	t.Run("Create Session", func(t *testing.T) {
		testSession1 = testutil.CreateTestSession(t, ts.App, ts.AuthToken.AccessToken)
	})

	t.Run("Get All Sessions", func(t *testing.T) {
		testSession2 = testutil.CreateTestSession(t, ts.App, ts.AuthToken.AccessToken)

		req := httptest.NewRequest("GET", "/v1/api/session", nil)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)
		resp, err := ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var sessions []session.WorkoutSessionDao
		err = json.NewDecoder(resp.Body).Decode(&sessions)
		require.NoError(t, err)
		assert.NotEmpty(t, sessions)

		for _, session := range sessions {
			if session.ID == testSession1.ID {
				assert.Equal(t, testSession1.ID, session.ID)
				assert.Equal(t, testSession1.Name, session.Name)
				assert.Equal(t, testSession1.Description, session.Description)
			} else if session.ID == testSession2.ID {
				assert.Equal(t, testSession2.ID, session.ID)
				assert.Equal(t, testSession2.Name, session.Name)
				assert.Equal(t, testSession2.Description, session.Description)
			}
		}
	})

	t.Run("Get Single Session", func(t *testing.T) {
		// Create a new session for this test
		testSession1 = testutil.CreateTestSession(t, ts.App, ts.AuthToken.AccessToken)

		req := httptest.NewRequest("GET", "/v1/api/session/"+strconv.Itoa(int(testSession1.ID)), nil)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)
		resp, err := ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var retrievedSession session.WorkoutSessionDao
		err = json.NewDecoder(resp.Body).Decode(&retrievedSession)
		require.NoError(t, err)
		assert.Equal(t, testSession1.ID, retrievedSession.ID)
		assert.Equal(t, testSession1.Name, retrievedSession.Name)
		assert.Equal(t, testSession1.Description, retrievedSession.Description)
	})

	t.Run("Update Session", func(t *testing.T) {
		// Create a new session for this test
		testSession1 = testutil.CreateTestSession(t, ts.App, ts.AuthToken.AccessToken)

		updatedSession := testSession1
		updatedSession.Name = "Updated Session"
		updatedSession.Description = "An updated test workout session"
		updatedSession.SessionGroups[0].Name = "Updated Warm Up"
		updatedSession.SessionGroups[0].Sets[0].Reps = 10
		updatedSession.SessionGroups[0].Sets[0].Weight = 100

		// Add a new session group
		updatedSession.SessionGroups = append(updatedSession.SessionGroups, session.SessionGroupDao{
			Name:        "New Session Group",
			Description: "A new session group",
			Order:       2,
			Sets: []session.SessionSetDao{
				{
					Order:  1,
					Reps:   10,
					Weight: 100,
				},
			},
		})

		jsonData, _ := json.Marshal(updatedSession)
		req := httptest.NewRequest("PUT", "/v1/api/session", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)
		resp, err := ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result session.WorkoutSessionDao
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)
		assert.Equal(t, updatedSession.Name, result.Name)
		assert.Equal(t, updatedSession.Description, result.Description)
		assert.Equal(t, updatedSession.SessionGroups[0].Name, result.SessionGroups[0].Name)

		// Verify the new session group was added
		assert.Equal(t, updatedSession.SessionGroups[1].Name, result.SessionGroups[1].Name)
		assert.Equal(t, updatedSession.SessionGroups[1].Sets[0].Reps, result.SessionGroups[1].Sets[0].Reps)
		assert.Equal(t, updatedSession.SessionGroups[1].Sets[0].Weight, result.SessionGroups[1].Sets[0].Weight)
	})

	t.Run("Delete Session", func(t *testing.T) {
		// Create a new session for this test
		testSession1 = testutil.CreateTestSession(t, ts.App, ts.AuthToken.AccessToken)

		req := httptest.NewRequest("DELETE", "/v1/api/session/"+strconv.Itoa(int(testSession1.ID)), nil)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)
		resp, err := ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		// Verify the session was deleted by trying to fetch it
		req = httptest.NewRequest("GET", "/v1/api/session/"+strconv.Itoa(int(testSession1.ID)), nil)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)
		resp, err = ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})
}
